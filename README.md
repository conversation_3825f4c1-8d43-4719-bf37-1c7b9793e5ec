# WebOS

A web-based operating system made with vanilla JavaScript designed to be simple and modular.

## Getting Started

```sh
git clone https://github.com/ButterDebugger/WebOS.git
cd WebOS
bun install
bun start
```

## Contributing

If you want to contribute to this project and make it better, your contributions would be much appreciated. Please first discuss any new changes you wish to make on a new issue. If you believe you can check anything off on our [TODO List](#todo-list), pull requests are welcomed :)

## TODO List:

-   Desktop files
    -   [x] Functionality to create desktop files
    -   [ ] Retain desktop files positions
    -   [ ] Prevent desktop files from being moved out of bounds
-   Windows
    -   [x] Functionality to create windows
    -   [x] Randomly position new windows
    -   [x] Icons for window buttons
        -   [x] Make sure the icon fits the container no matter the image size
    -   [x] Resizable windows
-   [ ] Start menu
-   [x] Context menu
    -   [x] Create context menu constructor
    -   [x] Make context menus disappear when clicked off
