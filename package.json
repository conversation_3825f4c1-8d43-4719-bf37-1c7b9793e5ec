{"name": "webos", "version": "1.0.0", "description": "An operating system all in the web browser", "type": "module", "scripts": {"dev": "bunx --bun vite", "build": "bunx --bun vite build --emptyOutDir", "start": "bun run build && bun run ./src/server.ts"}, "dependencies": {"@debutter/dough": "npm:@jsr/debutter__dough", "eventemitter3": "^5.0.1", "hono": "^4.7.10", "moment": "^2.30.1"}, "devDependencies": {"@types/node": "^22.15.29", "glob": "^11.0.2", "typescript": "~5.8.3", "vite": "^6.3.5"}, "license": "MIT", "author": "ButterDebugger", "repository": {"type": "git", "url": "git+https://github.com/ButterDebugger/WebOS.git"}, "contributors": []}