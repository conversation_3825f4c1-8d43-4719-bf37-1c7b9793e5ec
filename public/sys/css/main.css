@import url("https://debutter.dev/x/css/simple.css");

@font-face {
	font-family: "departure mono";
	src: url("/DepartureMono-1.500/DepartureMono-Regular.woff2") format("woff2");
}

:root {
	--c-menubar-bg: silver;
	--c-menubar-bg-dark: #b8b8b8;
	--c-menubar-bg-bright: #f6f6f6;
	--c-menubar-bright: #dfdfdf;
	--c-menubar-dark: gray;
	--un-menubar-thickness: 2px;
}

html,
body {
	height: 100%;
}

body {
	font-family: "departure mono", Arial, Helvetica, sans-serif;
	font-size: 11px;
	height: 100%;
	overflow: hidden;
	text-size-adjust: none;
	background-color: black;
}

*,
::before,
::after {
	box-sizing: border-box;
	font-variant-numeric: tabular-nums;
	text-rendering: optimizelegibility;
	user-select: none;
}

input,
button,
select,
textarea {
	font: inherit;
	letter-spacing: inherit;
	word-spacing: inherit;
}

/* Generic */
.bold-text {
	text-shadow: -1px 0px;
	letter-spacing: 1px;
}
.white-text {
	color: white;
	text-shadow: #245dda 0px 1px;
}

.flex-spacer {
	flex: 1;
}

.moveable {
	transition: filter 0.1s ease-in-out;
}
.moving {
	cursor: grabbing !important;
	filter: drop-shadow(rgba(0, 0, 0, 0.5) 3px 3px 0px);
}
.fix-drag {
	pointer-events: none;
}

/* Gui container */
.gray-container,
button {
	background-color: var(--c-menubar-bg);
	border-top: var(--un-menubar-thickness) solid var(--c-menubar-bright);
	border-left: var(--un-menubar-thickness) solid var(--c-menubar-bright);
	border-right: var(--un-menubar-thickness) solid var(--c-menubar-dark);
	border-bottom: var(--un-menubar-thickness) solid var(--c-menubar-dark);
}
button,
input[type="text"] {
	padding: calc(var(--un-menubar-thickness) * 2)
		calc(var(--un-menubar-thickness) * 4);
}
.white-inset,
input[type="text"],
.gray-inset,
button:active,
button.active {
	background-color: var(--c-menubar-bg-dark);
	border-top: var(--un-menubar-thickness) solid var(--c-menubar-dark);
	border-left: var(--un-menubar-thickness) solid var(--c-menubar-dark);
	border-right: var(--un-menubar-thickness) solid var(--c-menubar-bright);
	border-bottom: var(--un-menubar-thickness) solid var(--c-menubar-bright);
}
.white-inset,
input[type="text"] {
	background-color: var(--c-menubar-bg-bright);
}
button:focus-visible,
input[type="text"]:focus-visible {
	outline: 1px dotted black;
	outline-offset: calc(var(--un-menubar-thickness) * -2);
}
